{"logs": [{"outputFile": "com.example.cybertask.app-mergeDebugResources-30:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e1a27e7561c144f01ceeff24742ee3b6\\transformed\\core-1.13.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3413,3512,3614,3713,3813,3914,4020,9705", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "3507,3609,3708,3808,3909,4015,4132,9801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\84a7a8ce75fe03161c366781dd673fdc\\transformed\\appcompat-1.7.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,419,520,631,715,816,931,1011,1088,1181,1276,1368,1462,1564,1659,1756,1850,1943,2033,2115,2223,2327,2425,2531,2636,2741,2898,9386", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "414,515,626,710,811,926,1006,1083,1176,1271,1363,1457,1559,1654,1751,1845,1938,2028,2110,2218,2322,2420,2526,2631,2736,2893,2994,9463"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f980bf18c739eb0cd52cd8f63898f20a\\transformed\\material-1.12.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,334,411,493,586,673,770,899,983,1041,1104,1194,1263,1323,1414,1477,1541,1600,1667,1729,1784,1907,1965,2026,2081,2153,2290,2371,2451,2548,2629,2711,2841,2915,2989,3121,3207,3284,3335,3389,3455,3526,3603,3674,3753,3826,3900,3970,4044,4145,4231,4305,4394,4486,4560,4633,4722,4773,4853,4920,5003,5087,5149,5213,5276,5345,5439,5540,5633,5731,5786,5844,5922,6008,6085", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,74,76,81,92,86,96,128,83,57,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,79,96,80,81,129,73,73,131,85,76,50,53,65,70,76,70,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77,85,76,73", "endOffsets": "254,329,406,488,581,668,765,894,978,1036,1099,1189,1258,1318,1409,1472,1536,1595,1662,1724,1779,1902,1960,2021,2076,2148,2285,2366,2446,2543,2624,2706,2836,2910,2984,3116,3202,3279,3330,3384,3450,3521,3598,3669,3748,3821,3895,3965,4039,4140,4226,4300,4389,4481,4555,4628,4717,4768,4848,4915,4998,5082,5144,5208,5271,5340,5434,5535,5628,5726,5781,5839,5917,6003,6080,6154"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2999,3074,3151,3233,3326,4137,4234,4363,4447,4505,4568,4658,4727,4787,4878,4941,5005,5064,5131,5193,5248,5371,5429,5490,5545,5617,5754,5835,5915,6012,6093,6175,6305,6379,6453,6585,6671,6748,6799,6853,6919,6990,7067,7138,7217,7290,7364,7434,7508,7609,7695,7769,7858,7950,8024,8097,8186,8237,8317,8384,8467,8551,8613,8677,8740,8809,8903,9004,9097,9195,9250,9308,9468,9554,9631", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,74,76,81,92,86,96,128,83,57,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,79,96,80,81,129,73,73,131,85,76,50,53,65,70,76,70,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77,85,76,73", "endOffsets": "304,3069,3146,3228,3321,3408,4229,4358,4442,4500,4563,4653,4722,4782,4873,4936,5000,5059,5126,5188,5243,5366,5424,5485,5540,5612,5749,5830,5910,6007,6088,6170,6300,6374,6448,6580,6666,6743,6794,6848,6914,6985,7062,7133,7212,7285,7359,7429,7503,7604,7690,7764,7853,7945,8019,8092,8181,8232,8312,8379,8462,8546,8608,8672,8735,8804,8898,8999,9092,9190,9245,9303,9381,9549,9626,9700"}}]}]}