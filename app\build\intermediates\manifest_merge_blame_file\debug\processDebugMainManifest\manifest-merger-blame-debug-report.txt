1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.cybertask"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:5:22-64
12
13    <permission
13-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a27e7561c144f01ceeff24742ee3b6\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
14        android:name="com.example.cybertask.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
14-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a27e7561c144f01ceeff24742ee3b6\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
15        android:protectionLevel="signature" />
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a27e7561c144f01ceeff24742ee3b6\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
16
17    <uses-permission android:name="com.example.cybertask.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
17-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a27e7561c144f01ceeff24742ee3b6\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
17-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a27e7561c144f01ceeff24742ee3b6\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
18
19    <application
19-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:7:5-35:19
20        android:allowBackup="true"
20-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:8:9-35
21        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
21-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a27e7561c144f01ceeff24742ee3b6\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
22        android:dataExtractionRules="@xml/data_extraction_rules"
22-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:9:9-65
23        android:debuggable="true"
24        android:extractNativeLibs="false"
25        android:fullBackupContent="@xml/backup_rules"
25-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:10:9-54
26        android:icon="@mipmap/ic_launcher"
26-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:11:9-43
27        android:label="@string/app_name"
27-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:12:9-41
28        android:roundIcon="@mipmap/ic_launcher_round"
28-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:13:9-54
29        android:supportsRtl="true"
29-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:14:9-35
30        android:testOnly="true"
31        android:theme="@style/Theme.CyberTask" >
31-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:15:9-47
32        <activity
32-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:17:9-34:20
33            android:name="com.example.cybertask.MainActivity"
33-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:18:13-41
34            android:exported="true" >
34-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:19:13-36
35            <intent-filter>
35-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:20:13-24:29
36                <action android:name="android.intent.action.MAIN" />
36-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:21:17-69
36-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:21:25-66
37
38                <category android:name="android.intent.category.LAUNCHER" />
38-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:23:17-77
38-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:23:27-74
39            </intent-filter>
40
41            <!-- Intent filter for deep linking -->
42            <intent-filter android:autoVerify="true" >
42-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:27:13-33:29
42-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:27:28-53
43                <action android:name="android.intent.action.VIEW" />
43-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:28:17-69
43-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:28:25-66
44
45                <category android:name="android.intent.category.DEFAULT" />
45-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:29:17-76
45-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:29:27-73
46                <category android:name="android.intent.category.BROWSABLE" />
46-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:30:17-78
46-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:30:27-75
47
48                <data
48-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:31:17-32:60
49                    android:host="to-do-cyber.web.app"
49-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:32:23-57
50                    android:scheme="https" />
50-->C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\AndroidManifest.xml:31:23-45
51            </intent-filter>
52        </activity>
53
54        <provider
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
55            android:name="androidx.startup.InitializationProvider"
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
56            android:authorities="com.example.cybertask.androidx-startup"
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
57            android:exported="false" >
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
58            <meta-data
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.emoji2.text.EmojiCompatInitializer"
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
60                android:value="androidx.startup" />
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c21c12642c2c7e44f61eba9d502d25f7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
62-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c21c12642c2c7e44f61eba9d502d25f7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
63                android:value="androidx.startup" />
63-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c21c12642c2c7e44f61eba9d502d25f7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
64            <meta-data
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
65                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
66                android:value="androidx.startup" />
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
67        </provider>
68
69        <receiver
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
70            android:name="androidx.profileinstaller.ProfileInstallReceiver"
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
71            android:directBootAware="false"
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
72            android:enabled="true"
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
73            android:exported="true"
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
74            android:permission="android.permission.DUMP" >
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
76                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
79                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
82                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
85                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
86            </intent-filter>
87        </receiver>
88    </application>
89
90</manifest>
