com.example.cybertask.app-appcompat-resources-1.7.0-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\015d76796b45fd9c5d76dc49f93fa54d\transformed\appcompat-resources-1.7.0\res
com.example.cybertask.app-emoji2-views-helper-1.3.0-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\093a36a042e7595c18462f6d33b35933\transformed\emoji2-views-helper-1.3.0\res
com.example.cybertask.app-startup-runtime-1.1.1-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a7c4e7f5405e6eba5ce3292ef2988bd\transformed\startup-runtime-1.1.1\res
com.example.cybertask.app-lifecycle-runtime-2.6.2-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11acec76f4340d5dab5f543bc5a6c650\transformed\lifecycle-runtime-2.6.2\res
com.example.cybertask.app-lifecycle-livedata-core-2.6.2-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50c7d27ad49e2fd85e160d1fa740d420\transformed\lifecycle-livedata-core-2.6.2\res
com.example.cybertask.app-lifecycle-viewmodel-2.6.2-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6af8277e5d530355355182a25b1c2143\transformed\lifecycle-viewmodel-2.6.2\res
com.example.cybertask.app-annotation-experimental-1.4.0-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c432e43595acca53bab0d5931f466b0\transformed\annotation-experimental-1.4.0\res
com.example.cybertask.app-emoji2-1.3.0-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\res
com.example.cybertask.app-viewpager2-1.0.0-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\792d24f27595536e9c1fc63b551840a0\transformed\viewpager2-1.0.0\res
com.example.cybertask.app-core-viewtree-1.0.0-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bbdb819c29b538cc20a489b70be6ec5\transformed\core-viewtree-1.0.0\res
com.example.cybertask.app-appcompat-1.7.0-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84a7a8ce75fe03161c366781dd673fdc\transformed\appcompat-1.7.0\res
com.example.cybertask.app-constraintlayout-2.2.1-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e5d0b352d619a625f5332f0b4919f84\transformed\constraintlayout-2.2.1\res
com.example.cybertask.app-profileinstaller-1.4.0-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\res
com.example.cybertask.app-drawerlayout-1.1.1-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9469cb975477b011f72e9ca19b90195e\transformed\drawerlayout-1.1.1\res
com.example.cybertask.app-activity-1.10.1-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a69e30787bcac3d9d482634da5796f03\transformed\activity-1.10.1\res
com.example.cybertask.app-transition-1.5.0-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa51b17dcb9dbaae2776c5455d8f43b4\transformed\transition-1.5.0\res
com.example.cybertask.app-cardview-1.0.0-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc1a474ed1562887860e0a8c18d7b907\transformed\cardview-1.0.0\res
com.example.cybertask.app-lifecycle-process-2.6.2-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c21c12642c2c7e44f61eba9d502d25f7\transformed\lifecycle-process-2.6.2\res
com.example.cybertask.app-coordinatorlayout-1.1.0-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c31849d3b3b0b280ce32b9ecad1038dd\transformed\coordinatorlayout-1.1.0\res
com.example.cybertask.app-savedstate-1.2.1-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4e5d6c4239a785d239c74bef06ebdf8\transformed\savedstate-1.2.1\res
com.example.cybertask.app-recyclerview-1.1.0-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3ca7373e0d83cd8d081470c64f7a0fe\transformed\recyclerview-1.1.0\res
com.example.cybertask.app-core-ktx-1.13.0-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61d6a8ed519b1b06601a7f41b239fe8\transformed\core-ktx-1.13.0\res
com.example.cybertask.app-core-runtime-2.2.0-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d71235691b059965585b8f07683f0cc8\transformed\core-runtime-2.2.0\res
com.example.cybertask.app-fragment-1.5.4-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d85a329e7abc139712977f6da26b0c27\transformed\fragment-1.5.4\res
com.example.cybertask.app-lifecycle-viewmodel-savedstate-2.6.2-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddf99882b1c3341d3cc0e94b073b3d1d\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.example.cybertask.app-core-1.13.0-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a27e7561c144f01ceeff24742ee3b6\transformed\core-1.13.0\res
com.example.cybertask.app-material-1.12.0-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f980bf18c739eb0cd52cd8f63898f20a\transformed\material-1.12.0\res
com.example.cybertask.app-lifecycle-livedata-2.6.2-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd2f6452544a20e623be11ba2eff755c\transformed\lifecycle-livedata-2.6.2\res
com.example.cybertask.app-pngs-28 C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\build\generated\res\pngs\debug
com.example.cybertask.app-resValues-29 C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\build\generated\res\resValues\debug
com.example.cybertask.app-packageDebugResources-30 C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.cybertask.app-packageDebugResources-31 C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.cybertask.app-debug-32 C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.cybertask.app-debug-33 C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\debug\res
com.example.cybertask.app-main-34 C:\Users\<USER>\AndroidStudioProjects\CyberTask\app\src\main\res
